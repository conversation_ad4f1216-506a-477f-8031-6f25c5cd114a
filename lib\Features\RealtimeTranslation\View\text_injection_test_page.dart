import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../Core/Services/text_injection_service.dart';

class TextInjectionTestPage extends StatefulWidget {
  const TextInjectionTestPage({super.key});

  @override
  State<TextInjectionTestPage> createState() => _TextInjectionTestPageState();
}

class _TextInjectionTestPageState extends State<TextInjectionTestPage> {
  final TextInjectionService _injectionService = TextInjectionService.instance;
  final TextEditingController _textController = TextEditingController();
  final TextEditingController _packageController = TextEditingController();

  InjectionStrategy _selectedStrategy = InjectionStrategy.smart;
  bool _isInjecting = false;
  InputFieldInfo? _currentInputInfo;
  List<InjectionResult> _recentResults = [];

  @override
  void initState() {
    super.initState();
    _initializeService();
    _loadCurrentInputInfo();
    _listenToResults();
  }

  Future<void> _initializeService() async {
    // Service is automatically initialized
  }

  Future<void> _loadCurrentInputInfo() async {
    final info = await _injectionService.getCurrentInputFieldInfo();
    setState(() {
      _currentInputInfo = info;
      if (info?.packageName != null) {
        _packageController.text = info!.packageName!;
      }
    });
  }

  void _listenToResults() {
    _injectionService.resultsStream.listen((result) {
      setState(() {
        _recentResults.insert(0, result);
        if (_recentResults.length > 10) {
          _recentResults.removeLast();
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Text Injection Test'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadCurrentInputInfo,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInputFieldInfoCard(),
            SizedBox(height: 16.h),
            _buildInjectionControlCard(),
            SizedBox(height: 16.h),
            _buildConfigurationCard(),
            SizedBox(height: 16.h),
            _buildStatisticsCard(),
            SizedBox(height: 16.h),
            _buildRecentResultsCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildInputFieldInfoCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _currentInputInfo?.hasInputField == true
                      ? Icons.check_circle
                      : Icons.error,
                  color: _currentInputInfo?.hasInputField == true
                      ? Colors.green
                      : Colors.red,
                  size: 24.r,
                ),
                SizedBox(width: 8.w),
                Text(
                  'Input Field Status',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            if (_currentInputInfo != null) ...[
              _buildInfoRow(
                'Has Input Field',
                _currentInputInfo!.hasInputField.toString(),
              ),
              _buildInfoRow(
                'Package Name',
                _currentInputInfo!.packageName ?? 'Unknown',
              ),
              _buildInfoRow('Hint', _currentInputInfo!.hint ?? 'None'),
              _buildInfoRow(
                'Is Password',
                _currentInputInfo!.isPasswordField.toString(),
              ),
              _buildInfoRow(
                'Is Multiline',
                _currentInputInfo!.isMultiline.toString(),
              ),
              if (_currentInputInfo!.capabilities.isNotEmpty) ...[
                SizedBox(height: 8.h),
                Text(
                  'Capabilities:',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                ..._currentInputInfo!.capabilities.entries.map(
                  (entry) =>
                      _buildInfoRow('  ${entry.key}', entry.value.toString()),
                ),
              ],
            ] else ...[
              Text(
                'Loading input field information...',
                style: TextStyle(fontSize: 14.sp),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInjectionControlCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Text Injection Control',
              style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 16.h),

            // Text input
            TextField(
              controller: _textController,
              decoration: const InputDecoration(
                labelText: 'Text to inject',
                hintText: 'Enter text to inject into the target app',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            SizedBox(height: 12.h),

            // Package name input
            TextField(
              controller: _packageController,
              decoration: const InputDecoration(
                labelText: 'Target package (optional)',
                hintText: 'e.g., com.whatsapp',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: 12.h),

            // Strategy selection
            DropdownButtonFormField<InjectionStrategy>(
              value: _selectedStrategy,
              decoration: const InputDecoration(
                labelText: 'Injection Strategy',
                border: OutlineInputBorder(),
              ),
              items: InjectionStrategy.values
                  .map(
                    (strategy) => DropdownMenuItem(
                      value: strategy,
                      child: Text(strategy.name.toUpperCase()),
                    ),
                  )
                  .toList(),
              onChanged: (value) => setState(() => _selectedStrategy = value!),
            ),
            SizedBox(height: 16.h),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isInjecting ? null : _performInjection,
                    icon: _isInjecting
                        ? SizedBox(
                            width: 16.w,
                            height: 16.h,
                            child: const CircularProgressIndicator(
                              strokeWidth: 2,
                            ),
                          )
                        : const Icon(Icons.send),
                    label: Text(_isInjecting ? 'Injecting...' : 'Inject Text'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                    ),
                  ),
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _performTestInjection,
                    icon: const Icon(Icons.science),
                    label: const Text('Test Injection'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 12.h),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigurationCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Configuration',
              style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 16.h),

            ElevatedButton.icon(
              onPressed: _showConfigurationDialog,
              icon: const Icon(Icons.settings),
              label: const Text('Configure Injection Settings'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Statistics',
              style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 12.h),

            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Total Attempts',
                    _injectionService.totalAttempts.toString(),
                    Icons.send,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Successful',
                    _injectionService.successfulInjections.toString(),
                    Icons.check_circle,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Success Rate',
                    '${(_injectionService.successRate * 100).toStringAsFixed(1)}%',
                    Icons.trending_up,
                  ),
                ),
              ],
            ),

            SizedBox(height: 12.h),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _showDetailedStatistics,
                    icon: const Icon(Icons.analytics),
                    label: const Text('Detailed Stats'),
                  ),
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _clearHistory,
                    icon: const Icon(Icons.clear_all),
                    label: const Text('Clear History'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentResultsCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Results',
              style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 12.h),

            if (_recentResults.isEmpty)
              Text(
                'No recent injection attempts',
                style: TextStyle(fontSize: 14.sp),
              )
            else
              ..._recentResults.map((result) => _buildResultItem(result)),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2.h),
      child: Row(
        children: [
          SizedBox(
            width: 120.w,
            child: Text(
              '$label:',
              style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value, style: TextStyle(fontSize: 14.sp)),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: [
          Icon(icon, size: 24.r, color: Theme.of(context).primaryColor),
          SizedBox(height: 4.h),
          Text(
            value,
            style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.bold),
          ),
          Text(
            label,
            style: TextStyle(fontSize: 12.sp, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildResultItem(InjectionResult result) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 4.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: result.success ? Colors.green[50] : Colors.red[50],
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: result.success ? Colors.green[200]! : Colors.red[200]!,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                result.success ? Icons.check_circle : Icons.error,
                color: result.success ? Colors.green : Colors.red,
                size: 16.r,
              ),
              SizedBox(width: 8.w),
              Text(
                result.strategy.name.toUpperCase(),
                style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w600),
              ),
              const Spacer(),
              Text(
                '${result.duration.inMilliseconds}ms',
                style: TextStyle(fontSize: 12.sp, color: Colors.grey[600]),
              ),
            ],
          ),
          if (result.error != null) ...[
            SizedBox(height: 4.h),
            Text(
              result.error!,
              style: TextStyle(fontSize: 11.sp, color: Colors.red[700]),
            ),
          ],
        ],
      ),
    );
  }

  Future<void> _performInjection() async {
    if (_textController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter text to inject')),
      );
      return;
    }

    setState(() => _isInjecting = true);

    try {
      final result = await _injectionService.injectText(
        text: _textController.text,
        targetPackage: _packageController.text.isNotEmpty
            ? _packageController.text
            : 'unknown',
        strategy: _selectedStrategy,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              result.success
                  ? 'Text injected successfully!'
                  : 'Injection failed: ${result.error}',
            ),
            backgroundColor: result.success ? Colors.green : Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isInjecting = false);
    }
  }

  Future<void> _performTestInjection() async {
    final result = await _injectionService.testInjection(
      targetPackage: _packageController.text.isNotEmpty
          ? _packageController.text
          : null,
      strategy: _selectedStrategy,
    );

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            result.success
                ? 'Test injection successful!'
                : 'Test injection failed: ${result.error}',
          ),
          backgroundColor: result.success ? Colors.green : Colors.red,
        ),
      );
    }
  }

  void _showConfigurationDialog() {
    // Implementation for configuration dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Injection Configuration'),
        content: const Text('Configuration dialog would be implemented here'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showDetailedStatistics() {
    // Implementation for detailed statistics
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Detailed Statistics'),
        content: const Text('Detailed statistics would be shown here'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _clearHistory() {
    _injectionService.clearHistory();
    setState(() {
      _recentResults.clear();
    });
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('History cleared')));
  }

  @override
  void dispose() {
    _textController.dispose();
    _packageController.dispose();
    super.dispose();
  }
}
