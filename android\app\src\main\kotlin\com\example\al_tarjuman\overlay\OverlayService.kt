package com.example.al_tarjuman.overlay

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.graphics.Point
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.DisplayMetrics
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.OvershootInterpolator
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.example.al_tarjuman.models.ChatMessage
import java.util.concurrent.ConcurrentHashMap

class OverlayService : Service() {

    companion object {
        private const val TAG = "OverlayService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "overlay_service_channel"
        private const val AUTO_HIDE_DELAY = 5000L
        private const val ANIMATION_DURATION = 300L

        var instance: OverlayService? = null
    }

    private lateinit var windowManager: WindowManager
    private lateinit var displayMetrics: DisplayMetrics
    private val overlayViews = ConcurrentHashMap<String, OverlayViewHolder>()
    private val mainHandler = Handler(Looper.getMainLooper())
    private var screenWidth = 0
    private var screenHeight = 0

    // Overlay configuration
    private var overlayStyle = OverlayStyle.BUBBLE
    private var overlayPosition = OverlayPosition.SMART
    private var enableAnimations = true
    private var autoHideEnabled = true
    private var overlayOpacity = 0.9f

    // Data classes and enums
    enum class OverlayStyle {
        BUBBLE,      // Rounded bubble style
        CARD,        // Material card style
        MINIMAL,     // Minimal text-only style
        FLOATING     // Floating action button style
    }

    enum class OverlayPosition {
        SMART,       // Smart positioning based on message location
        TOP,         // Always at top
        BOTTOM,      // Always at bottom
        CENTER,      // Center of screen
        FOLLOW       // Follow the original message
    }

    data class OverlayViewHolder(
        val view: View,
        val layoutParams: WindowManager.LayoutParams,
        val message: ChatMessage,
        val createdAt: Long = System.currentTimeMillis(),
        var isAnimating: Boolean = false,
        var autoHideRunnable: Runnable? = null
    )
    
    override fun onCreate() {
        super.onCreate()
        instance = this
        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager

        // Get display metrics
        displayMetrics = resources.displayMetrics
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val windowMetrics = windowManager.currentWindowMetrics
            val bounds = windowMetrics.bounds
            screenWidth = bounds.width()
            screenHeight = bounds.height()
        } else {
            @Suppress("DEPRECATION")
            val display = windowManager.defaultDisplay
            val size = Point()
            @Suppress("DEPRECATION")
            display.getSize(size)
            screenWidth = size.x
            screenHeight = size.y
        }

        // Create notification channel for foreground service
        createNotificationChannel()

        Log.d(TAG, "OverlayService created - Screen: ${screenWidth}x${screenHeight}")
    }

    override fun onBind(intent: Intent?): IBinder? = null

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Log.d(TAG, "OverlayService started")

        // Start as foreground service to prevent being killed
        startForeground(NOTIFICATION_ID, createNotification())

        return START_STICKY
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Translation Overlay Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Service for displaying translation overlays"
                setShowBadge(false)
            }

            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createNotification(): Notification {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            Notification.Builder(this, CHANNEL_ID)
                .setContentTitle("Al Tarjuman")
                .setContentText("Translation overlay service is running")
                .setSmallIcon(android.R.drawable.ic_dialog_info)
                .setOngoing(true)
                .setCategory(Notification.CATEGORY_SERVICE)
                .build()
        } else {
            @Suppress("DEPRECATION")
            Notification.Builder(this)
                .setContentTitle("Al Tarjuman")
                .setContentText("Translation overlay service is running")
                .setSmallIcon(android.R.drawable.ic_dialog_info)
                .setOngoing(true)
                .setPriority(Notification.PRIORITY_LOW)
                .build()
        }
    }
    
    fun showTranslationOverlay(message: ChatMessage) {
        if (message.translatedText.isNullOrBlank()) return

        mainHandler.post {
            try {
                // Remove existing overlay for this message if any
                removeOverlay(message.id)

                // Create overlay view and layout params
                val overlayView = createOverlayView(message)
                val layoutParams = createOverlayLayoutParams(message)

                // Create view holder
                val viewHolder = OverlayViewHolder(overlayView, layoutParams, message)

                // Add to window manager
                windowManager.addView(overlayView, layoutParams)
                overlayViews[message.id] = viewHolder

                // Apply entrance animation
                if (enableAnimations) {
                    animateOverlayEntrance(viewHolder)
                }

                // Setup auto-hide
                if (autoHideEnabled) {
                    setupAutoHide(viewHolder)
                }

                Log.d(TAG, "Translation overlay shown for message: ${message.id}")

            } catch (e: Exception) {
                Log.e(TAG, "Failed to show translation overlay", e)
            }
        }
    }

    private fun animateOverlayEntrance(viewHolder: OverlayViewHolder) {
        val view = viewHolder.view
        viewHolder.isAnimating = true

        // Start with scale 0 and alpha 0
        view.scaleX = 0f
        view.scaleY = 0f
        view.alpha = 0f

        // Animate to full scale and alpha
        val scaleXAnimator = ObjectAnimator.ofFloat(view, "scaleX", 0f, 1f)
        val scaleYAnimator = ObjectAnimator.ofFloat(view, "scaleY", 0f, 1f)
        val alphaAnimator = ObjectAnimator.ofFloat(view, "alpha", 0f, overlayOpacity)

        listOf(scaleXAnimator, scaleYAnimator, alphaAnimator).forEach { animator ->
            animator.duration = ANIMATION_DURATION
            animator.interpolator = OvershootInterpolator(1.2f)
        }

        alphaAnimator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                viewHolder.isAnimating = false
            }
        })

        scaleXAnimator.start()
        scaleYAnimator.start()
        alphaAnimator.start()
    }

    private fun setupAutoHide(viewHolder: OverlayViewHolder) {
        val autoHideRunnable = Runnable {
            removeOverlay(viewHolder.message.id)
        }

        viewHolder.autoHideRunnable = autoHideRunnable
        mainHandler.postDelayed(autoHideRunnable, AUTO_HIDE_DELAY)
    }
    
    fun removeOverlay(messageId: String, animate: Boolean = true) {
        overlayViews[messageId]?.let { viewHolder ->
            mainHandler.post {
                try {
                    // Cancel auto-hide if scheduled
                    viewHolder.autoHideRunnable?.let { runnable ->
                        mainHandler.removeCallbacks(runnable)
                    }

                    if (animate && enableAnimations && !viewHolder.isAnimating) {
                        animateOverlayExit(viewHolder) {
                            finalizeOverlayRemoval(messageId, viewHolder)
                        }
                    } else {
                        finalizeOverlayRemoval(messageId, viewHolder)
                    }

                } catch (e: Exception) {
                    Log.e(TAG, "Failed to remove overlay", e)
                    // Fallback: force remove
                    finalizeOverlayRemoval(messageId, viewHolder)
                }
            }
        }
    }

    private fun animateOverlayExit(viewHolder: OverlayViewHolder, onComplete: () -> Unit) {
        val view = viewHolder.view
        viewHolder.isAnimating = true

        // Animate to scale 0 and alpha 0
        val scaleXAnimator = ObjectAnimator.ofFloat(view, "scaleX", 1f, 0f)
        val scaleYAnimator = ObjectAnimator.ofFloat(view, "scaleY", 1f, 0f)
        val alphaAnimator = ObjectAnimator.ofFloat(view, "alpha", view.alpha, 0f)

        listOf(scaleXAnimator, scaleYAnimator, alphaAnimator).forEach { animator ->
            animator.duration = ANIMATION_DURATION / 2 // Faster exit
            animator.interpolator = AccelerateDecelerateInterpolator()
        }

        alphaAnimator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                onComplete()
            }
        })

        scaleXAnimator.start()
        scaleYAnimator.start()
        alphaAnimator.start()
    }

    private fun finalizeOverlayRemoval(messageId: String, viewHolder: OverlayViewHolder) {
        try {
            windowManager.removeView(viewHolder.view)
            overlayViews.remove(messageId)
            Log.d(TAG, "Overlay removed for message: $messageId")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to finalize overlay removal", e)
        }
    }

    fun removeAllOverlays(animate: Boolean = false) {
        overlayViews.keys.toList().forEach { messageId ->
            removeOverlay(messageId, animate)
        }
    }
    
    private fun createOverlayView(message: ChatMessage): View {
        return when (overlayStyle) {
            OverlayStyle.BUBBLE -> createBubbleOverlay(message)
            OverlayStyle.CARD -> createCardOverlay(message)
            OverlayStyle.MINIMAL -> createMinimalOverlay(message)
            OverlayStyle.FLOATING -> createFloatingOverlay(message)
        }
    }

    private fun createBubbleOverlay(message: ChatMessage): View {
        val overlayView = createBaseOverlayView(message)

        // Apply bubble styling
        overlayView.background = createBubbleBackground(message.isIncoming)
        overlayView.setPadding(24, 16, 24, 16)

        return overlayView
    }

    private fun createCardOverlay(message: ChatMessage): View {
        val overlayView = createBaseOverlayView(message)

        // Apply card styling with elevation effect
        overlayView.background = createCardBackground(message.isIncoming)
        overlayView.setPadding(20, 16, 20, 16)
        overlayView.elevation = 8f

        return overlayView
    }

    private fun createMinimalOverlay(message: ChatMessage): View {
        val container = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(16, 12, 16, 12)
            background = createMinimalBackground(message.isIncoming)
        }

        val translationText = TextView(this).apply {
            text = message.translatedText
            textSize = 14f
            setTextColor(0xFFFFFFFF.toInt())
            maxLines = 3
        }

        container.addView(translationText)
        setupTouchHandling(container, message)

        return container
    }

    private fun createFloatingOverlay(message: ChatMessage): View {
        val container = LinearLayout(this).apply {
            orientation = LinearLayout.HORIZONTAL
            setPadding(12, 12, 12, 12)
            background = createFloatingBackground(message.isIncoming)
        }

        // Add translation icon
        val icon = ImageView(this).apply {
            setImageResource(android.R.drawable.ic_menu_translate)
            layoutParams = LinearLayout.LayoutParams(48, 48).apply {
                marginEnd = 12
            }
        }

        val textContainer = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            layoutParams = LinearLayout.LayoutParams(0, ViewGroup.LayoutParams.WRAP_CONTENT, 1f)
        }

        val translationText = TextView(this).apply {
            text = message.translatedText
            textSize = 13f
            setTextColor(0xFFFFFFFF.toInt())
            maxLines = 2
        }

        textContainer.addView(translationText)
        container.addView(icon)
        container.addView(textContainer)

        setupTouchHandling(container, message)

        return container
    }

    private fun createBaseOverlayView(message: ChatMessage): LinearLayout {
        val container = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
        }

        // Header with close button
        val header = LinearLayout(this).apply {
            orientation = LinearLayout.HORIZONTAL
            setPadding(0, 0, 0, 8)
        }

        val headerText = TextView(this).apply {
            text = "Translation"
            textSize = 12f
            setTextColor(0xFFFFFFFF.toInt())
            layoutParams = LinearLayout.LayoutParams(0, ViewGroup.LayoutParams.WRAP_CONTENT, 1f)
        }

        val closeButton = ImageView(this).apply {
            setImageResource(android.R.drawable.ic_menu_close_clear_cancel)
            layoutParams = LinearLayout.LayoutParams(32, 32)
            setOnClickListener { removeOverlay(message.id) }
            background = createRippleBackground()
        }

        header.addView(headerText)
        header.addView(closeButton)

        // Translation text
        val translationText = TextView(this).apply {
            text = message.translatedText
            textSize = 14f
            setTextColor(0xFFFFFFFF.toInt())
            maxLines = 4
            setPadding(0, 0, 0, 8)
        }

        // Original text (smaller)
        val originalText = TextView(this).apply {
            text = message.text
            textSize = 11f
            setTextColor(0xCCFFFFFF.toInt())
            maxLines = 2
            alpha = 0.8f
        }

        container.addView(header)
        container.addView(translationText)
        container.addView(originalText)

        setupTouchHandling(container, message)

        return container
    }

    private fun createBubbleBackground(isIncoming: Boolean) =
        createGradientDrawable(isIncoming, 24f)

    private fun createCardBackground(isIncoming: Boolean) =
        createGradientDrawable(isIncoming, 12f)

    private fun createMinimalBackground(isIncoming: Boolean) =
        createGradientDrawable(isIncoming, 8f)

    private fun createFloatingBackground(isIncoming: Boolean) =
        createGradientDrawable(isIncoming, 28f)

    private fun createGradientDrawable(isIncoming: Boolean, cornerRadius: Float): android.graphics.drawable.GradientDrawable {
        val color = if (isIncoming) {
            0xCC2196F3.toInt() // Blue for incoming
        } else {
            0xCC4CAF50.toInt() // Green for outgoing
        }

        return android.graphics.drawable.GradientDrawable().apply {
            setColor(color)
            this.cornerRadius = cornerRadius
            setStroke(2, 0x40FFFFFF)
        }
    }

    private fun createRippleBackground(): android.graphics.drawable.Drawable {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            android.graphics.drawable.RippleDrawable(
                android.content.res.ColorStateList.valueOf(0x40FFFFFF),
                null,
                null
            )
        } else {
            android.graphics.drawable.GradientDrawable().apply {
                setColor(0x40FFFFFF)
                cornerRadius = 16f
            }
        }
    }

    private fun setupTouchHandling(view: View, message: ChatMessage) {
        var initialX = 0f
        var initialY = 0f
        var initialTouchX = 0f
        var initialTouchY = 0f
        var isDragging = false

        view.setOnTouchListener { v, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    val viewHolder = overlayViews[message.id] ?: return@setOnTouchListener false
                    initialX = viewHolder.layoutParams.x.toFloat()
                    initialY = viewHolder.layoutParams.y.toFloat()
                    initialTouchX = event.rawX
                    initialTouchY = event.rawY
                    isDragging = false

                    // Cancel auto-hide while touching
                    viewHolder.autoHideRunnable?.let { runnable ->
                        mainHandler.removeCallbacks(runnable)
                    }

                    true
                }
                MotionEvent.ACTION_MOVE -> {
                    val deltaX = event.rawX - initialTouchX
                    val deltaY = event.rawY - initialTouchY

                    if (!isDragging && (Math.abs(deltaX) > 10 || Math.abs(deltaY) > 10)) {
                        isDragging = true
                    }

                    if (isDragging) {
                        val viewHolder = overlayViews[message.id] ?: return@setOnTouchListener false
                        viewHolder.layoutParams.x = (initialX + deltaX).toInt()
                        viewHolder.layoutParams.y = (initialY + deltaY).toInt()

                        // Keep within screen bounds
                        viewHolder.layoutParams.x = viewHolder.layoutParams.x.coerceIn(0, screenWidth - v.width)
                        viewHolder.layoutParams.y = viewHolder.layoutParams.y.coerceIn(0, screenHeight - v.height)

                        windowManager.updateViewLayout(v, viewHolder.layoutParams)
                    }
                    true
                }
                MotionEvent.ACTION_UP -> {
                    if (!isDragging) {
                        // Single tap - expand/collapse or show actions
                        handleOverlayTap(message)
                    } else {
                        // Snap to edge if dragged
                        snapToEdge(message)
                    }

                    // Resume auto-hide
                    val viewHolder = overlayViews[message.id]
                    if (viewHolder != null && autoHideEnabled) {
                        setupAutoHide(viewHolder)
                    }

                    true
                }
                else -> false
            }
        }
    }
    
    private fun handleOverlayTap(message: ChatMessage) {
        // Toggle between expanded and collapsed state or show action menu
        Log.d(TAG, "Overlay tapped for message: ${message.id}")
        // Could implement expand/collapse functionality here
    }

    private fun snapToEdge(message: ChatMessage) {
        val viewHolder = overlayViews[message.id] ?: return
        val view = viewHolder.view

        // Snap to the nearest edge (left or right)
        val centerX = viewHolder.layoutParams.x + view.width / 2
        val targetX = if (centerX < screenWidth / 2) {
            20 // Snap to left edge with margin
        } else {
            screenWidth - view.width - 20 // Snap to right edge with margin
        }

        // Animate to target position
        val animator = ValueAnimator.ofInt(viewHolder.layoutParams.x, targetX)
        animator.duration = 200
        animator.interpolator = AccelerateDecelerateInterpolator()
        animator.addUpdateListener { animation ->
            viewHolder.layoutParams.x = animation.animatedValue as Int
            windowManager.updateViewLayout(view, viewHolder.layoutParams)
        }
        animator.start()
    }

    private fun createOverlayLayoutParams(message: ChatMessage): WindowManager.LayoutParams {
        val layoutParams = WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                @Suppress("DEPRECATION")
                WindowManager.LayoutParams.TYPE_PHONE
            },
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                    WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH,
            PixelFormat.TRANSLUCENT
        )

        // Calculate position based on overlay position setting
        val position = calculateOverlayPosition(message)
        layoutParams.gravity = Gravity.TOP or Gravity.START
        layoutParams.x = position.first
        layoutParams.y = position.second

        return layoutParams
    }

    private fun calculateOverlayPosition(message: ChatMessage): Pair<Int, Int> {
        return when (overlayPosition) {
            OverlayPosition.SMART -> calculateSmartPosition(message)
            OverlayPosition.TOP -> Pair(screenWidth / 2 - 150, 100)
            OverlayPosition.BOTTOM -> Pair(screenWidth / 2 - 150, screenHeight - 200)
            OverlayPosition.CENTER -> Pair(screenWidth / 2 - 150, screenHeight / 2 - 100)
            OverlayPosition.FOLLOW -> Pair(message.bounds.left, message.bounds.bottom + 10)
        }
    }

    private fun calculateSmartPosition(message: ChatMessage): Pair<Int, Int> {
        val messageX = message.bounds.left
        val messageY = message.bounds.top
        val messageWidth = message.bounds.width()
        val messageHeight = message.bounds.height()

        // Estimate overlay size (will be adjusted after creation)
        val overlayWidth = 300
        val overlayHeight = 120

        // Try to position overlay without covering the original message
        var x = messageX
        var y = messageY + messageHeight + 20 // Below message by default

        // If overlay would go off-screen at bottom, try above
        if (y + overlayHeight > screenHeight - 100) {
            y = messageY - overlayHeight - 20
        }

        // If overlay would go off-screen at top, position at center
        if (y < 100) {
            y = screenHeight / 2 - overlayHeight / 2
        }

        // Adjust horizontal position to stay on screen
        if (x + overlayWidth > screenWidth - 20) {
            x = screenWidth - overlayWidth - 20
        }
        if (x < 20) {
            x = 20
        }

        // For incoming messages, prefer right side; for outgoing, prefer left side
        if (message.isIncoming && x + overlayWidth < screenWidth - 20) {
            x = minOf(messageX + messageWidth + 20, screenWidth - overlayWidth - 20)
        } else if (!message.isIncoming && x > 20) {
            x = maxOf(messageX - overlayWidth - 20, 20)
        }

        return Pair(x, y)
    }
    
    // Configuration methods
    fun setOverlayStyle(style: OverlayStyle) {
        overlayStyle = style
        Log.d(TAG, "Overlay style changed to: $style")
    }

    fun setOverlayPosition(position: OverlayPosition) {
        overlayPosition = position
        Log.d(TAG, "Overlay position changed to: $position")
    }

    fun setAnimationsEnabled(enabled: Boolean) {
        enableAnimations = enabled
        Log.d(TAG, "Animations ${if (enabled) "enabled" else "disabled"}")
    }

    fun setAutoHideEnabled(enabled: Boolean) {
        autoHideEnabled = enabled
        Log.d(TAG, "Auto-hide ${if (enabled) "enabled" else "disabled"}")
    }

    fun setOverlayOpacity(opacity: Float) {
        overlayOpacity = opacity.coerceIn(0.1f, 1.0f)
        Log.d(TAG, "Overlay opacity set to: $overlayOpacity")
    }

    // Status and debugging methods
    fun getOverlayCount(): Int = overlayViews.size

    fun getActiveOverlays(): List<String> = overlayViews.keys.toList()

    fun getOverlayInfo(messageId: String): Map<String, Any>? {
        return overlayViews[messageId]?.let { viewHolder ->
            mapOf(
                "messageId" to messageId,
                "createdAt" to viewHolder.createdAt,
                "isAnimating" to viewHolder.isAnimating,
                "x" to viewHolder.layoutParams.x,
                "y" to viewHolder.layoutParams.y,
                "width" to viewHolder.view.width,
                "height" to viewHolder.view.height
            )
        }
    }

    fun getServiceConfiguration(): Map<String, Any> {
        return mapOf(
            "overlayStyle" to overlayStyle.name,
            "overlayPosition" to overlayPosition.name,
            "enableAnimations" to enableAnimations,
            "autoHideEnabled" to autoHideEnabled,
            "overlayOpacity" to overlayOpacity,
            "screenWidth" to screenWidth,
            "screenHeight" to screenHeight,
            "activeOverlays" to getOverlayCount()
        )
    }

    // Batch operations
    fun hideAllOverlaysTemporarily(durationMs: Long = 3000) {
        val currentOverlays = overlayViews.keys.toList()
        currentOverlays.forEach { messageId ->
            overlayViews[messageId]?.view?.alpha = 0.3f
        }

        mainHandler.postDelayed({
            currentOverlays.forEach { messageId ->
                overlayViews[messageId]?.view?.alpha = overlayOpacity
            }
        }, durationMs)
    }

    fun repositionAllOverlays() {
        overlayViews.values.forEach { viewHolder ->
            val newPosition = calculateOverlayPosition(viewHolder.message)
            viewHolder.layoutParams.x = newPosition.first
            viewHolder.layoutParams.y = newPosition.second
            windowManager.updateViewLayout(viewHolder.view, viewHolder.layoutParams)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        removeAllOverlays(animate = false)
        mainHandler.removeCallbacksAndMessages(null)
        instance = null
        Log.d(TAG, "OverlayService destroyed")
    }
}
