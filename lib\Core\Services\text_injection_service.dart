import 'dart:async';
import 'package:flutter/foundation.dart';
import 'platform_channel_service.dart';

/// Flutter service for managing text injection operations
class TextInjectionService {
  static TextInjectionService? _instance;
  static TextInjectionService get instance =>
      _instance ??= TextInjectionService._();

  TextInjectionService._();

  final PlatformChannelService _platformService =
      PlatformChannelService.instance;

  final StreamController<InjectionResult> _resultsController =
      StreamController<InjectionResult>.broadcast();

  // Configuration
  InjectionStrategy _preferredStrategy = InjectionStrategy.smart;
  bool _enableRetries = true;
  bool _enableFallbacks = true;
  int _maxRetries = 3;
  Duration _timeout = const Duration(seconds: 10);

  // Statistics
  int _totalAttempts = 0;
  int _successfulInjections = 0;
  final List<InjectionResult> _injectionHistory = [];
  static const int _maxHistorySize = 1000;

  // Getters
  Stream<InjectionResult> get resultsStream => _resultsController.stream;
  List<InjectionResult> get injectionHistory =>
      List.unmodifiable(_injectionHistory);
  double get successRate =>
      _totalAttempts > 0 ? _successfulInjections / _totalAttempts : 0.0;
  int get totalAttempts => _totalAttempts;
  int get successfulInjections => _successfulInjections;

  /// Helper method to add result to history with size management
  void _addToHistory(InjectionResult result) {
    _injectionHistory.add(result);
    _resultsController.add(result);

    if (_injectionHistory.length > _maxHistorySize) {
      _injectionHistory.removeAt(0);
    }
  }

  /// Inject text into the current input field
  Future<InjectionResult> injectText({
    required String text,
    required String targetPackage,
    InjectionStrategy? strategy,
    Duration? timeout,
  }) async {
    final startTime = DateTime.now();
    _totalAttempts++;

    try {
      final result = await _platformService.injectTextAdvanced(
        text: text,
        targetPackage: targetPackage,
        strategy: (strategy ?? _preferredStrategy).name,
        timeout: (timeout ?? _timeout).inMilliseconds,
        enableRetries: _enableRetries,
        enableFallbacks: _enableFallbacks,
        maxRetries: _maxRetries,
      );

      final injectionResult = InjectionResult.fromMap(result);

      if (injectionResult.success) {
        _successfulInjections++;
      }

      _addToHistory(injectionResult);

      return injectionResult;
    } catch (e) {
      final errorResult = InjectionResult(
        requestId: 'error_${DateTime.now().millisecondsSinceEpoch}',
        success: false,
        strategy: strategy ?? _preferredStrategy,
        duration: DateTime.now().difference(startTime),
        error: e.toString(),
        timestamp: DateTime.now(),
      );

      _addToHistory(errorResult);

      return errorResult;
    }
  }

  /// Check if text injection is currently possible
  Future<bool> canInjectText() async {
    try {
      return await _platformService.canInjectText();
    } catch (e) {
      debugPrint('Error checking injection capability: $e');
      return false;
    }
  }

  /// Get current input field information
  Future<InputFieldInfo?> getCurrentInputFieldInfo() async {
    try {
      final result = await _platformService.getCurrentInputFieldInfo();
      if (result.isNotEmpty) {
        return InputFieldInfo.fromMap(result);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting input field info: $e');
      return null;
    }
  }

  /// Get injection statistics
  Future<Map<String, dynamic>> getStatistics() async {
    try {
      return await _platformService.getInjectionStatistics();
    } catch (e) {
      debugPrint('Error getting injection statistics: $e');
      return {};
    }
  }

  /// Get recommended strategy for a specific app
  Future<InjectionStrategy> getRecommendedStrategy(String packageName) async {
    try {
      final result = await _platformService.getRecommendedStrategy(packageName);

      return InjectionStrategy.values.firstWhere(
        (strategy) => strategy.name == result,
        orElse: () => InjectionStrategy.smart,
      );
    } catch (e) {
      debugPrint('Error getting recommended strategy: $e');
      return InjectionStrategy.smart;
    }
  }

  /// Configure injection behavior
  Future<void> configure({
    InjectionStrategy? preferredStrategy,
    bool? enableRetries,
    bool? enableFallbacks,
    int? maxRetries,
    Duration? timeout,
  }) async {
    if (preferredStrategy != null) _preferredStrategy = preferredStrategy;
    if (enableRetries != null) _enableRetries = enableRetries;
    if (enableFallbacks != null) _enableFallbacks = enableFallbacks;
    if (maxRetries != null) _maxRetries = maxRetries.clamp(1, 10);
    if (timeout != null) _timeout = timeout;

    try {
      await _platformService.configureTextInjection(
        preferredStrategy: _preferredStrategy.name,
        enableRetries: _enableRetries,
        enableFallbacks: _enableFallbacks,
        maxRetries: _maxRetries,
        timeout: _timeout.inMilliseconds,
      );
    } catch (e) {
      debugPrint('Error configuring text injection: $e');
    }
  }

  /// Test text injection with sample text
  Future<InjectionResult> testInjection({
    String? targetPackage,
    InjectionStrategy? strategy,
  }) async {
    final testText = 'Test injection at ${DateTime.now().toIso8601String()}';

    return injectText(
      text: testText,
      targetPackage: targetPackage ?? 'com.example.test',
      strategy: strategy,
    );
  }

  /// Clear injection history
  void clearHistory() {
    _injectionHistory.clear();
    _totalAttempts = 0;
    _successfulInjections = 0;
  }

  /// Get injection history filtered by criteria
  List<InjectionResult> getFilteredHistory({
    bool? successOnly,
    InjectionStrategy? strategy,
    String? targetPackage,
    DateTime? after,
    DateTime? before,
  }) {
    return _injectionHistory.where((result) {
      if (successOnly == true && !result.success) return false;
      if (strategy != null && result.strategy != strategy) return false;
      if (targetPackage != null && result.targetPackage != targetPackage) {
        return false;
      }
      if (after != null && result.timestamp.isBefore(after)) return false;
      if (before != null && result.timestamp.isAfter(before)) return false;
      return true;
    }).toList();
  }

  /// Export injection history as JSON
  Map<String, dynamic> exportHistory() {
    return {
      'totalAttempts': _totalAttempts,
      'successfulInjections': _successfulInjections,
      'successRate': successRate,
      'exportTime': DateTime.now().toIso8601String(),
      'history': _injectionHistory.map((result) => result.toMap()).toList(),
    };
  }

  /// Dispose resources
  void dispose() {
    _resultsController.close();
  }
}

/// Text injection strategies
enum InjectionStrategy {
  smart,
  setText,
  clipboardPaste,
  simulateTyping,
  gestureBased,
  hybrid,
  ime,
}

/// Result of a text injection operation
class InjectionResult {
  final String requestId;
  final bool success;
  final InjectionStrategy strategy;
  final Duration duration;
  final String? error;
  final int retryCount;
  final DateTime timestamp;
  final String? targetPackage;

  const InjectionResult({
    required this.requestId,
    required this.success,
    required this.strategy,
    required this.duration,
    this.error,
    this.retryCount = 0,
    required this.timestamp,
    this.targetPackage,
  });

  factory InjectionResult.fromMap(Map<dynamic, dynamic> map) {
    return InjectionResult(
      requestId: map['requestId']?.toString() ?? '',
      success: map['success'] as bool? ?? false,
      strategy: InjectionStrategy.values.firstWhere(
        (s) => s.name == map['strategy'],
        orElse: () => InjectionStrategy.smart,
      ),
      duration: Duration(milliseconds: map['duration'] as int? ?? 0),
      error: map['error']?.toString(),
      retryCount: map['retryCount'] as int? ?? 0,
      timestamp: DateTime.fromMillisecondsSinceEpoch(
        map['timestamp'] as int? ?? DateTime.now().millisecondsSinceEpoch,
      ),
      targetPackage: map['targetPackage']?.toString(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'requestId': requestId,
      'success': success,
      'strategy': strategy.name,
      'duration': duration.inMilliseconds,
      'error': error,
      'retryCount': retryCount,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'targetPackage': targetPackage,
    };
  }
}

/// Information about the current input field
class InputFieldInfo {
  final bool hasInputField;
  final String? packageName;
  final String? hint;
  final String? currentText;
  final bool isPasswordField;
  final bool isMultiline;
  final Map<String, bool> capabilities;

  const InputFieldInfo({
    required this.hasInputField,
    this.packageName,
    this.hint,
    this.currentText,
    this.isPasswordField = false,
    this.isMultiline = false,
    this.capabilities = const {},
  });

  factory InputFieldInfo.fromMap(Map<dynamic, dynamic> map) {
    return InputFieldInfo(
      hasInputField: map['hasInputField'] as bool? ?? false,
      packageName: map['packageName']?.toString(),
      hint: map['hint']?.toString(),
      currentText: map['currentText']?.toString(),
      isPasswordField: map['isPasswordField'] as bool? ?? false,
      isMultiline: map['isMultiline'] as bool? ?? false,
      capabilities: Map<String, bool>.from(map['capabilities'] ?? {}),
    );
  }
}
