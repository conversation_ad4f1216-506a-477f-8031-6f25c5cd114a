import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../Features/RealtimeTranslation/widgets/overlay_manager_widget.dart';

/// Flutter-based overlay service that manages in-app overlays
class FlutterOverlayService {
  static FlutterOverlayService? _instance;
  static FlutterOverlayService get instance =>
      _instance ??= FlutterOverlayService._();

  FlutterOverlayService._();

  final List<OverlayEntry> _activeOverlays = [];
  final StreamController<List<TranslationMessage>> _messagesController =
      StreamController<List<TranslationMessage>>.broadcast();

  OverlayConfig _config = const OverlayConfig();
  final List<TranslationMessage> _messages = [];

  // Getters
  Stream<List<TranslationMessage>> get messagesStream =>
      _messagesController.stream;
  OverlayConfig get config => _config;
  List<TranslationMessage> get messages => List.unmodifiable(_messages);
  int get activeOverlayCount => _activeOverlays.length;

  /// Update overlay configuration
  void updateConfig(OverlayConfig newConfig) {
    _config = newConfig;
    _notifyConfigChange();
  }

  /// Show a translation overlay
  void showTranslationOverlay({
    required String messageId,
    required String originalText,
    required String translatedText,
    required bool isIncoming,
    Rect? originalBounds,
  }) {
    _messages.removeWhere((msg) => msg.id == messageId);

    final message = TranslationMessage(
      id: messageId,
      originalText: originalText,
      translatedText: translatedText,
      isIncoming: isIncoming,
      timestamp: DateTime.now(),
      originalBounds: originalBounds,
    );

    _addMessage(message);
  }

  /// Helper method to add message with size management
  void _addMessage(TranslationMessage message) {
    _messages.add(message);

    while (_messages.length > _config.maxConcurrentOverlays) {
      _messages.removeAt(0);
    }

    _messagesController.add(List.from(_messages));
    HapticFeedback.lightImpact();
  }

  /// Remove a specific overlay
  void removeOverlay(String messageId) {
    _messages.removeWhere((msg) => msg.id == messageId);
    _messagesController.add(List.from(_messages));
  }

  /// Remove all overlays
  void removeAllOverlays() {
    _messages.clear();
    _messagesController.add(List.from(_messages));
  }

  /// Show overlay using Flutter's Overlay widget
  void showFlutterOverlay({
    required BuildContext context,
    required Widget overlayWidget,
    String? overlayId,
  }) {
    final overlay = Overlay.of(context);
    final overlayEntry = OverlayEntry(builder: (context) => overlayWidget);

    overlay.insert(overlayEntry);
    _activeOverlays.add(overlayEntry);
  }

  /// Remove Flutter overlay
  void removeFlutterOverlay(OverlayEntry overlayEntry) {
    overlayEntry.remove();
    _activeOverlays.remove(overlayEntry);
  }

  /// Remove all Flutter overlays
  void removeAllFlutterOverlays() {
    for (final overlay in _activeOverlays) {
      overlay.remove();
    }
    _activeOverlays.clear();
  }

  /// Create a positioned overlay widget
  Widget createPositionedOverlay({
    required Widget child,
    Alignment alignment = Alignment.topCenter,
    EdgeInsets margin = EdgeInsets.zero,
  }) {
    return Positioned.fill(
      child: Align(
        alignment: alignment,
        child: Container(margin: margin, child: child),
      ),
    );
  }

  /// Create an overlay manager widget for integration
  Widget createOverlayManager({
    Function(String)? onOverlayClose,
    Function(String)? onOverlayTap,
  }) {
    return StreamBuilder<List<TranslationMessage>>(
      stream: messagesStream,
      initialData: _messages,
      builder: (context, snapshot) {
        return OverlayManagerWidget(
          config: _config,
          messages: snapshot.data ?? [],
          onOverlayClose: (messageId) {
            removeOverlay(messageId);
            onOverlayClose?.call(messageId);
          },
          onOverlayTap: onOverlayTap,
        );
      },
    );
  }

  /// Get overlay statistics
  Map<String, dynamic> getStatistics() {
    return {
      'activeMessages': _messages.length,
      'activeFlutterOverlays': _activeOverlays.length,
      'config': {
        'style': _config.style.name,
        'position': _config.position.name,
        'enableAnimations': _config.enableAnimations,
        'autoHideDuration': _config.autoHideDuration?.inMilliseconds,
        'opacity': _config.opacity,
        'showOriginalText': _config.showOriginalText,
        'showActions': _config.showActions,
        'maxConcurrentOverlays': _config.maxConcurrentOverlays,
      },
      'messagesByType': {
        'incoming': _messages.where((msg) => msg.isIncoming).length,
        'outgoing': _messages.where((msg) => !msg.isIncoming).length,
      },
    };
  }

  /// Export overlay history
  List<Map<String, dynamic>> exportHistory() {
    return _messages
        .map(
          (message) => {
            'id': message.id,
            'originalText': message.originalText,
            'translatedText': message.translatedText,
            'isIncoming': message.isIncoming,
            'timestamp': message.timestamp.toIso8601String(),
            'bounds': message.originalBounds != null
                ? {
                    'left': message.originalBounds!.left,
                    'top': message.originalBounds!.top,
                    'right': message.originalBounds!.right,
                    'bottom': message.originalBounds!.bottom,
                  }
                : null,
          },
        )
        .toList();
  }

  /// Clear overlay history
  void clearHistory() {
    removeAllOverlays();
  }

  /// Temporarily hide all overlays
  void hideAllTemporarily({Duration duration = const Duration(seconds: 3)}) {
    final currentMessages = List<TranslationMessage>.from(_messages);
    removeAllOverlays();

    Timer(duration, () {
      _messages.addAll(currentMessages);
      _messagesController.add(List.from(_messages));
    });
  }

  /// Batch operations
  void addMultipleMessages(List<TranslationMessage> messages) {
    for (final message in messages) {
      _addMessage(message);
    }
  }

  /// Filter messages by criteria
  List<TranslationMessage> filterMessages({
    bool? isIncoming,
    DateTime? after,
    DateTime? before,
    String? textContains,
  }) {
    return _messages.where((message) {
      if (isIncoming != null && message.isIncoming != isIncoming) return false;
      if (after != null && message.timestamp.isBefore(after)) return false;
      if (before != null && message.timestamp.isAfter(before)) return false;
      if (textContains != null &&
          !message.originalText.toLowerCase().contains(
            textContains.toLowerCase(),
          ) &&
          !message.translatedText.toLowerCase().contains(
            textContains.toLowerCase(),
          )) {
        return false;
      }
      return true;
    }).toList();
  }

  /// Get message by ID
  TranslationMessage? getMessageById(String messageId) {
    try {
      return _messages.firstWhere((msg) => msg.id == messageId);
    } catch (e) {
      return null;
    }
  }

  /// Update existing message
  void updateMessage(
    String messageId, {
    String? originalText,
    String? translatedText,
  }) {
    final index = _messages.indexWhere((msg) => msg.id == messageId);
    if (index != -1) {
      final oldMessage = _messages[index];
      _messages[index] = TranslationMessage(
        id: oldMessage.id,
        originalText: originalText ?? oldMessage.originalText,
        translatedText: translatedText ?? oldMessage.translatedText,
        isIncoming: oldMessage.isIncoming,
        timestamp: oldMessage.timestamp,
        originalBounds: oldMessage.originalBounds,
      );
      _messagesController.add(List.from(_messages));
    }
  }

  void _notifyConfigChange() {
    // Notify any listeners about config changes
    _messagesController.add(List.from(_messages));
  }

  /// Dispose resources
  void dispose() {
    removeAllOverlays();
    removeAllFlutterOverlays();
    _messagesController.close();
  }
}
