import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../Core/Services/text_injection_service.dart';

class TextInjectionDemoPage extends StatefulWidget {
  const TextInjectionDemoPage({super.key});

  @override
  State<TextInjectionDemoPage> createState() => _TextInjectionDemoPageState();
}

class _TextInjectionDemoPageState extends State<TextInjectionDemoPage> {
  final TextInjectionService _injectionService = TextInjectionService.instance;
  final TextEditingController _textController = TextEditingController();

  bool _isInjecting = false;
  List<InjectionResult> _recentResults = [];

  final List<String> _sampleTexts = [
    'Hello, how are you?',
    'مرحبا، كيف حالك؟',
    'Good morning!',
    'صباح الخير!',
    'Thank you very much',
    'شكرا جزيلا',
    'See you later',
    'أراك لاحقا',
  ];

  @override
  void initState() {
    super.initState();
    _initializeService();
    _listenToResults();
  }

  Future<void> _initializeService() async {
    // Service is automatically initialized
  }

  void _listenToResults() {
    _injectionService.resultsStream.listen((result) {
      setState(() {
        _recentResults.insert(0, result);
        if (_recentResults.length > 5) {
          _recentResults.removeLast();
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Text Injection Demo'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildQuickTestCard(),
            SizedBox(height: 16.h),
            _buildCustomTextCard(),
            SizedBox(height: 16.h),
            _buildRecentResultsCard(),
            SizedBox(height: 16.h),
            _buildStatisticsCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickTestCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Test',
              style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 12.h),
            Text(
              'Tap any sample text to inject it into the currently focused input field:',
              style: TextStyle(fontSize: 14.sp),
            ),
            SizedBox(height: 12.h),
            Wrap(
              spacing: 8.w,
              runSpacing: 8.h,
              children: _sampleTexts
                  .map((text) => _buildSampleTextChip(text))
                  .toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSampleTextChip(String text) {
    return ActionChip(
      label: Text(text, style: TextStyle(fontSize: 12.sp)),
      onPressed: _isInjecting ? null : () => _injectSampleText(text),
      backgroundColor: Colors.blue[50],
      side: BorderSide(color: Colors.blue[200]!),
    );
  }

  Widget _buildCustomTextCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Custom Text Injection',
              style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 12.h),
            TextField(
              controller: _textController,
              decoration: const InputDecoration(
                labelText: 'Enter text to inject',
                hintText: 'Type your message here...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            SizedBox(height: 12.h),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isInjecting || _textController.text.isEmpty
                    ? null
                    : () => _injectCustomText(),
                icon: _isInjecting
                    ? SizedBox(
                        width: 16.w,
                        height: 16.h,
                        child: const CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.send),
                label: Text(_isInjecting ? 'Injecting...' : 'Inject Text'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentResultsCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Results',
              style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 12.h),
            if (_recentResults.isEmpty)
              Text(
                'No injection attempts yet',
                style: TextStyle(fontSize: 14.sp, color: Colors.grey[600]),
              )
            else
              ..._recentResults.map((result) => _buildResultItem(result)),
          ],
        ),
      ),
    );
  }

  Widget _buildResultItem(InjectionResult result) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 4.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: result.success ? Colors.green[50] : Colors.red[50],
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: result.success ? Colors.green[200]! : Colors.red[200]!,
        ),
      ),
      child: Row(
        children: [
          Icon(
            result.success ? Icons.check_circle : Icons.error,
            color: result.success ? Colors.green : Colors.red,
            size: 20.r,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  result.strategy.name.toUpperCase(),
                  style: TextStyle(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (result.error != null)
                  Text(
                    result.error!,
                    style: TextStyle(fontSize: 11.sp, color: Colors.red[700]),
                  ),
              ],
            ),
          ),
          Text(
            '${result.duration.inMilliseconds}ms',
            style: TextStyle(fontSize: 11.sp, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Statistics',
              style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 12.h),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Total',
                    _injectionService.totalAttempts.toString(),
                    Icons.send,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Success',
                    _injectionService.successfulInjections.toString(),
                    Icons.check_circle,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Rate',
                    '${(_injectionService.successRate * 100).toStringAsFixed(0)}%',
                    Icons.trending_up,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: [
          Icon(icon, size: 20.r, color: Theme.of(context).primaryColor),
          SizedBox(height: 4.h),
          Text(
            value,
            style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold),
          ),
          Text(
            label,
            style: TextStyle(fontSize: 10.sp, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Future<void> _injectSampleText(String text) async {
    await _performInjection(text);
  }

  Future<void> _injectCustomText() async {
    await _performInjection(_textController.text);
    _textController.clear();
  }

  Future<void> _performInjection(String text) async {
    setState(() => _isInjecting = true);

    try {
      final result = await _injectionService.injectText(
        text: text,
        targetPackage: 'unknown', // Will be detected automatically
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              result.success
                  ? 'Text injected successfully!'
                  : 'Injection failed: ${result.error}',
            ),
            backgroundColor: result.success ? Colors.green : Colors.red,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isInjecting = false);
      }
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }
}
