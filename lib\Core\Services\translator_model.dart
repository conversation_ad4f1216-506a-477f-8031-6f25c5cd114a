import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:translator/translator.dart';
import '../../Config/Cubit/app_cubit.dart';

class TranslationService {
  static final GoogleTranslator _translator = GoogleTranslator();
  static final Map<String, String> _translationCache = {};
  static final Map<String, String> _languageCache = {};

  // Cache size limits to prevent memory issues
  static const int _maxCacheSize = 1000;
  static const int _maxLanguageCacheSize = 500;

  /// Adds item to cache with size management
  static void _addToCache(
    Map<String, String> cache,
    String key,
    String value,
    int maxSize,
  ) {
    if (cache.length >= maxSize) {
      // Remove oldest entries (simple FIFO approach)
      final keysToRemove = cache.keys.take(cache.length - maxSize + 1).toList();
      for (final keyToRemove in keysToRemove) {
        cache.remove(keyToRemove);
      }
    }
    cache[key] = value;
  }

  static Future<String> translateText(
    String text,
    BuildContext context,
    String languageCode,
  ) async {
    if (text.isEmpty || !context.read<AppCubit>().state.internet) {
      return text;
    }

    // Check cache first
    final cacheKey = '${text.hashCode}_$languageCode';
    if (_translationCache.containsKey(cacheKey)) {
      return _translationCache[cacheKey]!;
    }

    try {
      Translation translation = await _translator.translate(
        text,
        to: languageCode,
      );

      // Cache the result with size management
      _addToCache(_translationCache, cacheKey, translation.text, _maxCacheSize);

      return translation.text;
    } catch (e) {
      return text;
    }
  }

  // Enhanced method for real-time chat translation
  static Future<Map<String, dynamic>> translateChatMessage({
    required String text,
    required String targetLanguage,
    String? sourceLanguage,
    bool shouldDetectLanguage = true,
  }) async {
    if (text.isEmpty) {
      return {
        'originalText': text,
        'translatedText': text,
        'sourceLanguage': 'unknown',
        'targetLanguage': targetLanguage,
        'success': false,
        'error': 'Empty text',
      };
    }

    try {
      String detectedLanguage = sourceLanguage ?? 'auto';

      // Detect language if not provided
      if (shouldDetectLanguage && sourceLanguage == null) {
        detectedLanguage = await detectLanguage(text);
      }

      // Don't translate if source and target are the same
      if (detectedLanguage == targetLanguage) {
        return {
          'originalText': text,
          'translatedText': text,
          'sourceLanguage': detectedLanguage,
          'targetLanguage': targetLanguage,
          'success': true,
          'error': null,
        };
      }

      // Check cache
      final cacheKey = '${text.hashCode}_${detectedLanguage}_$targetLanguage';
      if (_translationCache.containsKey(cacheKey)) {
        return {
          'originalText': text,
          'translatedText': _translationCache[cacheKey]!,
          'sourceLanguage': detectedLanguage,
          'targetLanguage': targetLanguage,
          'success': true,
          'error': null,
        };
      }

      // Perform translation
      Translation translation = await _translator.translate(
        text,
        from: detectedLanguage,
        to: targetLanguage,
      );

      // Cache the result with size management
      _addToCache(_translationCache, cacheKey, translation.text, _maxCacheSize);

      return {
        'originalText': text,
        'translatedText': translation.text,
        'sourceLanguage': translation.sourceLanguage.code,
        'targetLanguage': targetLanguage,
        'success': true,
        'error': null,
      };
    } catch (e) {
      return {
        'originalText': text,
        'translatedText': text,
        'sourceLanguage': sourceLanguage ?? 'unknown',
        'targetLanguage': targetLanguage,
        'success': false,
        'error': e.toString(),
      };
    }
  }

  // Language detection method
  static Future<String> detectLanguage(String text) async {
    if (text.isEmpty) return 'unknown';

    // Check cache first
    final cacheKey = text.hashCode.toString();
    if (_languageCache.containsKey(cacheKey)) {
      return _languageCache[cacheKey]!;
    }

    try {
      // Use Google Translator's detection
      final translation = await _translator.translate(text, to: 'en');
      final detectedLang = translation.sourceLanguage.code;

      // Cache the result with size management
      _addToCache(
        _languageCache,
        cacheKey,
        detectedLang,
        _maxLanguageCacheSize,
      );

      return detectedLang;
    } catch (e) {
      return 'unknown';
    }
  }

  // Clear caches to prevent memory issues
  static void clearCache() {
    _translationCache.clear();
    _languageCache.clear();
  }

  // Get cache size for monitoring
  static int getCacheSize() {
    return _translationCache.length + _languageCache.length;
  }
}

extension StringExtension on String {
  Future<String> translateText(
    BuildContext context,
    String languageCode,
  ) async =>
      await TranslationService.translateText(this, context, languageCode);
}
